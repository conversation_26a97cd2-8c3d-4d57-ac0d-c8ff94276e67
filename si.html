<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深企信银行版APP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf9 100%);
        }
        .card-hover:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        button:active {
            transform: scale(0.95);
            transition: transform 0.1s ease;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 主容器 -->
    <div class="flex flex-col min-h-screen max-w-6xl mx-auto">
        <!-- 顶部导航栏 -->
        <header class="bg-blue-600 text-white p-4 shadow-md">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-xl font-bold">深企信银行版</h1>
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-full hover:bg-blue-700 transition">
                        <i class="fas fa-bell"></i>
                    </button>
                    <button class="p-2 rounded-full hover:bg-blue-700 transition">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="flex-1 container mx-auto p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- 背景装饰元素 -->
            <div class="fixed top-20 right-10 w-32 h-32 rounded-full bg-blue-200 opacity-20 blur-3xl -z-10"></div>
            <div class="fixed bottom-20 left-10 w-48 h-48 rounded-full bg-purple-200 opacity-20 blur-3xl -z-10"></div>
            
            <!-- 欢迎横幅 -->
            <div class="col-span-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg p-4 mb-6 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-xl font-bold">欢迎回来，张经理！</h2>
                        <p class="text-blue-100">今天是2023年6月15日，星期四</p>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold">15</div>
                        <div class="text-blue-100 text-sm">今日待办</div>
                    </div>
                </div>
            </div>
            
            <!-- 查企业页面 -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 transform transition duration-300 hover:scale-105">
                <div class="p-4 bg-blue-500 text-white font-bold">查企业</div>
                <div class="p-4">
                    <div class="mb-4">
                        <div class="relative">
                            <input type="text" placeholder="输入企业名称" class="w-full p-3 border border-gray-300 rounded-lg pl-10 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition">
                            <i class="fas fa-search absolute left-3 top-3.5 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-blue-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-building text-blue-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">企业工商信息查询</h3>
                                    <p class="text-sm text-gray-600">查询企业基本信息、股权结构等</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-blue-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-file-alt text-blue-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">企业报告查询</h3>
                                    <p class="text-sm text-gray-600">查看企业各类报告（需授权）</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <h4 class="font-semibold mb-2">热门搜索</h4>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition cursor-pointer">腾讯科技</span>
                            <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition cursor-pointer">阿里巴巴</span>
                            <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition cursor-pointer">华为技术</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 拓客户页面 -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 transform transition duration-300 hover:scale-105">
                <div class="p-4 bg-green-500 text-white font-bold">拓客户</div>
                <div class="p-4">
                    <div class="space-y-3">
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-green-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-list text-green-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">白名单筛选</h3>
                                    <p class="text-sm text-gray-600">按类型筛选优质企业</p>
                                    <div class="mt-2 flex flex-wrap gap-1">
                                        <span class="px-2 py-1 bg-green-200 text-green-800 rounded text-xs">科创贷</span>
                                        <span class="px-2 py-1 bg-green-200 text-green-800 rounded text-xs">创新中小</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-green-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-map-marked-alt text-green-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">地图筛选</h3>
                                    <p class="text-sm text-gray-600">基于地理位置查找企业</p>
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <i class="fas fa-map-pin mr-1"></i>
                                        <span>深圳市南山区</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-green-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-sliders-h text-green-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">高级搜索</h3>
                                    <p class="text-sm text-gray-600">多条件精准筛选企业</p>
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <span>注册资本、行业类型等</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 做业务页面 -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 transform transition duration-300 hover:scale-105">
                <div class="p-4 bg-purple-500 text-white font-bold">做业务</div>
                <div class="p-4">
                    <div class="space-y-3">
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-purple-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-users text-purple-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">客户管理</h3>
                                    <p class="text-sm text-gray-600">查看拜访客户列表</p>
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <span>今日拜访: 3家</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-purple-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-file-signature text-purple-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">授权管理</h3>
                                    <p class="text-sm text-gray-600">管理已授权客户</p>
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <span>有效授权: 12家</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-purple-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-chart-bar text-purple-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">工作统计</h3>
                                    <p class="text-sm text-gray-600">查看工作数据统计</p>
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <span>本周拜访: 15家</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-purple-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-tasks text-purple-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">任务清单</h3>
                                    <p class="text-sm text-gray-600">查看客户拜访任务</p>
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <span>待完成: 5项</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-purple-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-start">
                                <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-gift text-purple-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold">企业福利</h3>
                                    <p class="text-sm text-gray-600">查看并发送企业福利</p>
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <span>可发送福利: 8项</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的页面 -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 transform transition duration-300 hover:scale-105">
                <div class="p-4 bg-yellow-500 text-white font-bold">我</div>
                <div class="p-4">
                    <div class="flex flex-col items-center mb-4 pb-4 border-b border-gray-200">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 mb-2 flex items-center justify-center text-white text-2xl font-bold transform transition duration-300 hover:scale-110">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3 class="font-semibold text-lg">张经理</h3>
                        <p class="text-gray-600 text-sm">客户经理</p>
                        <div class="mt-2 flex space-x-4">
                            <div class="text-center">
                                <div class="font-bold">15</div>
                                <div class="text-xs text-gray-500">今日拜访</div>
                            </div>
                            <div class="text-center">
                                <div class="font-bold">8</div>
                                <div class="text-xs text-gray-500">待办任务</div>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-yellow-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-center">
                                <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-question-circle text-yellow-600"></i>
                                </div>
                                <h3 class="font-semibold">帮助中心</h3>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-yellow-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-center">
                                <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-cog text-yellow-600"></i>
                                </div>
                                <h3 class="font-semibold">设置</h3>
                            </div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-yellow-50 transition cursor-pointer transform hover:-translate-y-1 duration-200">
                            <div class="flex items-center">
                                <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-sign-out-alt text-yellow-600"></i>
                                </div>
                                <h3 class="font-semibold">退出登录</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部导航栏 -->
        <footer class="bg-white border-t border-gray-200 p-4">
            <div class="container mx-auto">
                <div class="flex justify-around">
                    <button class="flex flex-col items-center text-blue-600">
                        <i class="fas fa-search text-xl mb-1"></i>
                        <span class="text-xs">查企业</span>
                    </button>
                    <button class="flex flex-col items-center text-gray-500">
                        <i class="fas fa-users text-xl mb-1"></i>
                        <span class="text-xs">拓客户</span>
                    </button>
                    <button class="flex flex-col items-center text-gray-500">
                        <i class="fas fa-briefcase text-xl mb-1"></i>
                        <span class="text-xs">做业务</span>
                    </button>
                    <button class="flex flex-col items-center text-gray-500">
                        <i class="fas fa-user text-xl mb-1"></i>
                        <span class="text-xs">我</span>
                    </button>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>