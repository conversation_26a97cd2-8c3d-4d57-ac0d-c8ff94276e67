<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深企信银行版APP - UI/UX设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bank-blue': '#1e40af',
                        'bank-light-blue': '#3b82f6',
                        'bank-dark': '#1e293b',
                        'bank-gray': '#64748b',
                        'bank-light-gray': '#f1f5f9'
                    }
                }
            }
        }
    </script>
    <style>
        .mockup-frame {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin: 10px;
            overflow: hidden;
        }
        .phone-frame {
            width: 375px;
            height: 667px;
            position: relative;
        }
        .page-container {
            display: inline-block;
            vertical-align: top;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-bank-dark mb-2">深企信银行版APP - UI/UX设计</h1>
        <p class="text-bank-gray">高质感银行客户经理移动应用界面设计</p>
    </div>

    <div class="flex flex-wrap justify-center">
        
        <!-- 1. 启动页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-gradient-to-br from-bank-blue to-bank-light-blue flex flex-col items-center justify-center text-white relative">
                    <div class="absolute top-0 left-0 w-full h-full opacity-10">
                        <img src="https://images.unsplash.com/photo-*************-c627a92ad1ab?w=400&h=600&fit=crop" alt="城市背景" class="w-full h-full object-cover">
                    </div>
                    <div class="z-10 text-center">
                        <div class="w-24 h-24 bg-white rounded-full flex items-center justify-center mb-6 mx-auto">
                            <svg class="w-12 h-12 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                            </svg>
                        </div>
                        <h1 class="text-3xl font-bold mb-2">深企信</h1>
                        <p class="text-lg opacity-90">银行客户经理专业版</p>
                        <div class="mt-8">
                            <div class="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">启动页</p>
        </div>

        <!-- 2. 登录页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-white flex flex-col">
                    <!-- 顶部装饰 -->
                    <div class="h-48 bg-gradient-to-br from-bank-blue to-bank-light-blue relative">
                        <img src="https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=200&fit=crop" alt="银行建筑" class="w-full h-full object-cover opacity-20">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-4 mx-auto">
                                    <svg class="w-8 h-8 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9z"/>
                                    </svg>
                                </div>
                                <h2 class="text-xl font-bold">深企信登录</h2>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 登录表单 -->
                    <div class="flex-1 px-8 py-8">
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">工号</label>
                                <input type="text" placeholder="请输入您的工号" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">密码</label>
                                <input type="password" placeholder="请输入密码" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue">
                                    <span class="ml-2 text-sm text-bank-gray">记住密码</span>
                                </label>
                                <a href="#" class="text-sm text-bank-blue">忘记密码？</a>
                            </div>
                            <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                                登录
                            </button>
                        </div>
                        
                        <div class="mt-8 text-center">
                            <p class="text-xs text-bank-gray">
                                登录即表示同意 <a href="#" class="text-bank-blue">用户协议</a> 和 <a href="#" class="text-bank-blue">隐私政策</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">登录页</p>
        </div>

        <!-- 3. 查企业主页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部搜索栏 -->
                    <div class="bg-bank-blue text-white px-4 py-4">
                        <div class="flex items-center justify-between mb-3">
                            <h1 class="text-lg font-semibold">查企业</h1>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
                            </svg>
                        </div>
                        <div class="relative">
                            <input type="text" placeholder="请输入企业名称" class="w-full px-4 py-3 rounded-lg text-bank-dark placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white">
                            <button class="absolute right-2 top-2 bg-bank-blue text-white px-4 py-1 rounded-md text-sm">搜索</button>
                        </div>
                    </div>

                    <!-- 快捷功能 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="text-sm font-semibold text-bank-dark mb-3">快捷查询</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <button class="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                                <div class="w-8 h-8 bg-bank-blue rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="text-left">
                                    <p class="text-sm font-medium text-bank-dark">工商信息</p>
                                    <p class="text-xs text-bank-gray">基础信息查询</p>
                                </div>
                            </button>
                            <button class="flex items-center p-3 bg-green-50 rounded-lg border border-green-100">
                                <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                    </svg>
                                </div>
                                <div class="text-left">
                                    <p class="text-sm font-medium text-bank-dark">授权报告</p>
                                    <p class="text-xs text-bank-gray">需客户授权</p>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- 最近查询 -->
                    <div class="mx-4 mt-4 flex-1">
                        <h3 class="text-sm font-semibold text-bank-dark mb-3">最近查询</h3>
                        <div class="space-y-3">
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="font-medium text-bank-dark">深圳市腾讯计算机系统有限公司</p>
                                        <p class="text-sm text-bank-gray mt-1">91440300708461136T</p>
                                        <p class="text-xs text-bank-gray mt-1">查询时间：2024-01-15 14:30</p>
                                    </div>
                                    <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="font-medium text-bank-dark">华为技术有限公司</p>
                                        <p class="text-sm text-bank-gray mt-1">91440300708461137U</p>
                                        <p class="text-xs text-bank-gray mt-1">查询时间：2024-01-15 10:20</p>
                                    </div>
                                    <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="mt-auto bg-white border-t border-gray-200">
                        <div class="grid grid-cols-4 py-2">
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-blue mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-xs text-bank-blue font-medium">查企业</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">拓客户</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">做业务</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">我</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">查企业主页</p>
        </div>

        <!-- 4. 企业详情页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                            </svg>
                            <h1 class="text-lg font-semibold">企业详情</h1>
                        </div>
                    </div>

                    <!-- 企业基本信息 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-bank-blue rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                <span class="text-white font-bold">腾</span>
                            </div>
                            <div class="flex-1">
                                <h2 class="font-semibold text-bank-dark">深圳市腾讯计算机系统有限公司</h2>
                                <p class="text-sm text-bank-gray mt-1">91440300708461136T</p>
                                <div class="flex items-center mt-2">
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">存续</span>
                                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full ml-2">高新技术企业</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 功能菜单 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm overflow-hidden">
                        <div class="divide-y divide-gray-100">
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">工商信息</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">股权信息</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                        </svg>
                                    </div>
                                    <div class="text-left">
                                        <p class="text-bank-dark font-medium">征信报告</p>
                                        <p class="text-xs text-orange-600">需客户授权</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                        </svg>
                                    </div>
                                    <div class="text-left">
                                        <p class="text-bank-dark font-medium">财务报告</p>
                                        <p class="text-xs text-orange-600">需客户授权</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="mx-4 mt-4 space-y-3">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            发送授权链接
                        </button>
                        <button class="w-full bg-white border border-bank-blue text-bank-blue py-3 rounded-lg font-medium">
                            添加到客户列表
                        </button>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">企业详情页</p>
        </div>

        <!-- 5. 授权申请页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                            </svg>
                            <h1 class="text-lg font-semibold">发送授权申请</h1>
                        </div>
                    </div>

                    <!-- 企业信息 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-bank-blue rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">腾</span>
                            </div>
                            <div>
                                <p class="font-medium text-bank-dark">深圳市腾讯计算机系统有限公司</p>
                                <p class="text-sm text-bank-gray">91440300708461136T</p>
                            </div>
                        </div>
                    </div>

                    <!-- 授权类型选择 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="font-semibold text-bank-dark mb-4">请选择需要授权的报告类型</h3>
                        <div class="space-y-3">
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-3">
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark">征信报告</p>
                                    <p class="text-sm text-bank-gray">企业信用状况详细报告</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-3">
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark">财务报告</p>
                                    <p class="text-sm text-bank-gray">企业财务状况分析报告</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-3">
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark">经营报告</p>
                                    <p class="text-sm text-bank-gray">企业经营状况综合报告</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- 联系方式 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="font-semibold text-bank-dark mb-4">联系方式</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">联系人</label>
                                <input type="text" placeholder="请输入联系人姓名" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">手机号码</label>
                                <input type="tel" placeholder="请输入手机号码" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">邮箱地址</label>
                                <input type="email" placeholder="请输入邮箱地址" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- 发送按钮 -->
                    <div class="mx-4 mt-4">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            发送授权链接
                        </button>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">授权申请页</p>
        </div>

    </div>
</body>
</html>
