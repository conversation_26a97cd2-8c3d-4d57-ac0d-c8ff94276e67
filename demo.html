<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深企信银行版APP - UI/UX设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bank-blue': '#1e40af',
                        'bank-light-blue': '#3b82f6',
                        'bank-dark': '#1e293b',
                        'bank-gray': '#64748b',
                        'bank-light-gray': '#f1f5f9'
                    }
                }
            }
        }
    </script>
    <style>
        .mockup-frame {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin: 10px;
            overflow: hidden;
        }
        .phone-frame {
            width: 375px;
            min-height: 667px;
            position: relative;
        }
        .page-container {
            display: inline-block;
            vertical-align: top;
            margin-bottom: 20px;
        }
        /* 移除固定高度限制，让内容自适应 */
        .h-full {
            min-height: 667px !important;
            height: auto !important;
        }
        /* 确保flex-1元素不会被压缩 */
        .flex-1 {
            flex: none !important;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-bank-dark mb-2">深企信银行版APP - UI/UX设计</h1>
        <p class="text-bank-gray">高质感银行客户经理移动应用界面设计</p>
    </div>

    <div class="flex flex-wrap justify-center">
        
        <!-- 1. 启动页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-gradient-to-br from-bank-blue to-bank-light-blue flex flex-col items-center justify-center text-white relative">
                    <div class="absolute top-0 left-0 w-full h-full opacity-10">
                        <img src="https://images.unsplash.com/photo-*************-c627a92ad1ab?w=400&h=600&fit=crop" alt="城市背景" class="w-full h-full object-cover">
                    </div>
                    <div class="z-10 text-center">
                        <div class="w-24 h-24 bg-white rounded-full flex items-center justify-center mb-6 mx-auto">
                            <svg class="w-12 h-12 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                            </svg>
                        </div>
                        <h1 class="text-3xl font-bold mb-2">深企信</h1>
                        <p class="text-lg opacity-90">银行客户经理专业版</p>
                        <div class="mt-8">
                            <div class="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">启动页</p>
        </div>

        <!-- 2. 登录页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-white flex flex-col">
                    <!-- 顶部装饰 -->
                    <div class="h-48 bg-gradient-to-br from-bank-blue to-bank-light-blue relative">
                        <img src="https://images.unsplash.com/photo-*************-c627a92ad1ab?w=400&h=200&fit=crop" alt="现代建筑" class="w-full h-full object-cover opacity-15">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-4 mx-auto">
                                    <svg class="w-8 h-8 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9z"/>
                                    </svg>
                                </div>
                                <h2 class="text-xl font-bold">深企信登录</h2>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 登录表单 -->
                    <div class="flex-1 px-8 py-8">
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">工号</label>
                                <input type="text" placeholder="请输入您的工号" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">密码</label>
                                <input type="password" placeholder="请输入密码" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue">
                                    <span class="ml-2 text-sm text-bank-gray">记住密码</span>
                                </label>
                                <a href="#" class="text-sm text-bank-blue">忘记密码？</a>
                            </div>
                            <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                                登录
                            </button>
                        </div>
                        
                        <div class="mt-8 text-center">
                            <p class="text-xs text-bank-gray">
                                登录即表示同意 <a href="#" class="text-bank-blue">用户协议</a> 和 <a href="#" class="text-bank-blue">隐私政策</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">登录页</p>
        </div>

        <!-- 3. 查企业主页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部搜索栏 -->
                    <div class="bg-bank-blue text-white px-4 py-4">
                        <div class="flex items-center justify-between mb-3">
                            <h1 class="text-lg font-semibold">查企业</h1>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
                            </svg>
                        </div>
                        <div class="relative">
                            <input type="text" placeholder="请输入企业名称" class="w-full px-4 py-3 rounded-lg text-bank-dark placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white">
                            <button class="absolute right-2 top-2 bg-bank-blue text-white px-4 py-1 rounded-md text-sm">搜索</button>
                        </div>
                    </div>

                    <!-- 快捷功能 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="text-sm font-semibold text-bank-dark mb-3">快捷查询</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <button class="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                                <div class="w-8 h-8 bg-bank-blue rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="text-left">
                                    <p class="text-sm font-medium text-bank-dark">工商信息</p>
                                    <p class="text-xs text-bank-gray">基础信息查询</p>
                                </div>
                            </button>
                            <button class="flex items-center p-3 bg-green-50 rounded-lg border border-green-100">
                                <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                    </svg>
                                </div>
                                <div class="text-left">
                                    <p class="text-sm font-medium text-bank-dark">授权报告</p>
                                    <p class="text-xs text-bank-gray">需客户授权</p>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- 最近查询 -->
                    <div class="mx-4 mt-4 flex-1">
                        <h3 class="text-sm font-semibold text-bank-dark mb-3">最近查询</h3>
                        <div class="space-y-3">
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="font-medium text-bank-dark">深圳市腾讯计算机系统有限公司</p>
                                        <p class="text-sm text-bank-gray mt-1">91440300708461136T</p>
                                        <p class="text-xs text-bank-gray mt-1">查询时间：2024-01-15 14:30</p>
                                    </div>
                                    <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="font-medium text-bank-dark">华为技术有限公司</p>
                                        <p class="text-sm text-bank-gray mt-1">91440300708461137U</p>
                                        <p class="text-xs text-bank-gray mt-1">查询时间：2024-01-15 10:20</p>
                                    </div>
                                    <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="mt-auto bg-white border-t border-gray-200">
                        <div class="grid grid-cols-4 py-2">
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-blue mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-xs text-bank-blue font-medium">查企业</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">拓客户</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">做业务</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">我</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">查企业主页</p>
        </div>

        <!-- 4. 企业详情页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                            </svg>
                            <h1 class="text-lg font-semibold">企业详情</h1>
                        </div>
                    </div>

                    <!-- 企业基本信息 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-bank-blue rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                <span class="text-white font-bold">腾</span>
                            </div>
                            <div class="flex-1">
                                <h2 class="font-semibold text-bank-dark">深圳市腾讯计算机系统有限公司</h2>
                                <p class="text-sm text-bank-gray mt-1">91440300708461136T</p>
                                <div class="flex items-center mt-2">
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">存续</span>
                                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full ml-2">高新技术企业</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 功能菜单 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm overflow-hidden">
                        <div class="divide-y divide-gray-100">
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">工商信息</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">股权信息</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                        </svg>
                                    </div>
                                    <div class="text-left">
                                        <p class="text-bank-dark font-medium">征信报告</p>
                                        <p class="text-xs text-orange-600">需客户授权</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                        </svg>
                                    </div>
                                    <div class="text-left">
                                        <p class="text-bank-dark font-medium">财务报告</p>
                                        <p class="text-xs text-orange-600">需客户授权</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="mx-4 mt-4 space-y-3">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            发送授权链接
                        </button>
                        <button class="w-full bg-white border border-bank-blue text-bank-blue py-3 rounded-lg font-medium">
                            添加到客户列表
                        </button>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">企业详情页</p>
        </div>

        <!-- 5. 授权申请页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                            </svg>
                            <h1 class="text-lg font-semibold">发送授权申请</h1>
                        </div>
                    </div>

                    <!-- 企业信息 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-bank-blue rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">腾</span>
                            </div>
                            <div>
                                <p class="font-medium text-bank-dark">深圳市腾讯计算机系统有限公司</p>
                                <p class="text-sm text-bank-gray">91440300708461136T</p>
                            </div>
                        </div>
                    </div>

                    <!-- 授权类型选择 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="font-semibold text-bank-dark mb-4">请选择需要授权的报告类型</h3>
                        <div class="space-y-3">
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-3">
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark">征信报告</p>
                                    <p class="text-sm text-bank-gray">企业信用状况详细报告</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-3">
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark">财务报告</p>
                                    <p class="text-sm text-bank-gray">企业财务状况分析报告</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-3">
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark">经营报告</p>
                                    <p class="text-sm text-bank-gray">企业经营状况综合报告</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- 联系方式 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="font-semibold text-bank-dark mb-4">联系方式</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">联系人</label>
                                <input type="text" placeholder="请输入联系人姓名" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">手机号码</label>
                                <input type="tel" placeholder="请输入手机号码" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-bank-dark mb-2">邮箱地址</label>
                                <input type="email" placeholder="请输入邮箱地址" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- 发送按钮 -->
                    <div class="mx-4 mt-4">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            发送授权链接
                        </button>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">授权申请页</p>
        </div>

        <!-- 6. 拓客户页面 (Tab切换版本) -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <h1 class="text-lg font-semibold mb-3">拓客户</h1>
                        <div class="relative">
                            <input type="text" placeholder="搜索企业名称或关键词" class="w-full px-4 py-2 rounded-lg text-bank-dark placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white text-sm">
                            <svg class="absolute right-3 top-2 w-4 h-4 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Tab切换 -->
                    <div class="bg-white border-b border-gray-200">
                        <div class="flex">
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-bank-blue text-bank-blue font-medium text-sm">白名单</button>
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-transparent text-bank-gray text-sm">地图筛选</button>
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-transparent text-bank-gray text-sm">高级搜索</button>
                        </div>
                    </div>

                    <!-- 白名单内容 -->
                    <div class="flex-1 flex flex-col">
                        <!-- 筛选标签 -->
                        <div class="bg-white px-4 py-3 border-b border-gray-200">
                            <div class="flex space-x-2 overflow-x-auto">
                                <button class="bg-bank-blue text-white px-3 py-1 rounded-full text-sm whitespace-nowrap">全部</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">科创贷</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">创新中小</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">高新技术</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">专精特新</button>
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div class="bg-white mx-4 mt-3 rounded-xl p-3 shadow-sm">
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <p class="text-xl font-bold text-bank-blue">1,248</p>
                                    <p class="text-xs text-bank-gray">总客户数</p>
                                </div>
                                <div>
                                    <p class="text-xl font-bold text-green-600">156</p>
                                    <p class="text-xs text-bank-gray">本月新增</p>
                                </div>
                                <div>
                                    <p class="text-xl font-bold text-orange-600">89</p>
                                    <p class="text-xs text-bank-gray">待联系</p>
                                </div>
                            </div>
                        </div>

                        <!-- 客户列表 -->
                        <div class="flex-1 px-4 py-3">
                            <div class="space-y-3">
                                <div class="bg-white rounded-lg p-3 shadow-sm">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-1">
                                                <h3 class="font-medium text-bank-dark text-sm">深圳市智能科技有限公司</h3>
                                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full ml-2">科创贷</span>
                                            </div>
                                            <p class="text-xs text-bank-gray mb-1">91440300MA5XXXXX | 张三 | 1000万</p>
                                            <p class="text-xs text-bank-gray mb-1">深圳市南山区科技园</p>
                                            <div class="flex items-center space-x-3">
                                                <span class="text-xs text-bank-gray">📞 138****8888</span>
                                                <span class="text-xs text-green-600">✓ 已认证</span>
                                            </div>
                                        </div>
                                        <div class="flex flex-col space-y-1 ml-2">
                                            <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">详情</button>
                                            <button class="bg-green-600 text-white px-2 py-1 rounded text-xs">联系</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-lg p-3 shadow-sm">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-1">
                                                <h3 class="font-medium text-bank-dark text-sm">广州创新制造股份公司</h3>
                                                <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full ml-2">专精特新</span>
                                            </div>
                                            <p class="text-xs text-bank-gray mb-1">91440100MA5YYYYY | 李四 | 5000万</p>
                                            <p class="text-xs text-bank-gray mb-1">广州市天河区珠江新城</p>
                                            <div class="flex items-center space-x-3">
                                                <span class="text-xs text-bank-gray">📞 139****9999</span>
                                                <span class="text-xs text-orange-600">⏳ 待认证</span>
                                            </div>
                                        </div>
                                        <div class="flex flex-col space-y-1 ml-2">
                                            <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">详情</button>
                                            <button class="bg-green-600 text-white px-2 py-1 rounded text-xs">联系</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-lg p-3 shadow-sm">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-1">
                                                <h3 class="font-medium text-bank-dark text-sm">东莞市新能源科技公司</h3>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-2">高新技术</span>
                                            </div>
                                            <p class="text-xs text-bank-gray mb-1">91441900MA5ZZZZZ | 王五 | 2000万</p>
                                            <p class="text-xs text-bank-gray mb-1">东莞市松山湖高新区</p>
                                            <div class="flex items-center space-x-3">
                                                <span class="text-xs text-bank-gray">📞 137****7777</span>
                                                <span class="text-xs text-green-600">✓ 已认证</span>
                                            </div>
                                        </div>
                                        <div class="flex flex-col space-y-1 ml-2">
                                            <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">详情</button>
                                            <button class="bg-green-600 text-white px-2 py-1 rounded text-xs">联系</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="mt-auto bg-white border-t border-gray-200">
                        <div class="grid grid-cols-4 py-2">
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">查企业</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-blue mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                </svg>
                                <span class="text-xs text-bank-blue font-medium">拓客户</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">做业务</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">我</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">拓客户页面 - 白名单</p>
        </div>

        <!-- 7. 拓客户页面 - 地图筛选 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <h1 class="text-lg font-semibold mb-3">拓客户</h1>
                        <div class="relative">
                            <input type="text" placeholder="搜索企业名称或关键词" class="w-full px-4 py-2 rounded-lg text-bank-dark placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white text-sm">
                            <svg class="absolute right-3 top-2 w-4 h-4 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Tab切换 -->
                    <div class="bg-white border-b border-gray-200">
                        <div class="flex">
                             <button class="flex-1 py-3 px-2 text-center border-b-2 border-transparent text-bank-gray text-sm">白名单</button>
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-bank-blue text-bank-blue font-medium text-sm">地图筛选</button>
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-transparent text-bank-gray text-sm">高级搜索</button>
                        </div>
                    </div>

                    <!-- 地图筛选内容 -->
                    <div class="flex-1 flex flex-col">
                        <!-- 地图区域 -->
                        <div class="relative h-48 bg-gray-300">
                            <img src="https://images.unsplash.com/photo-**********-423995f22d0b?w=400&h=200&fit=crop" alt="地图" class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>

                            <!-- 地图标记点 -->
                            <div class="absolute top-12 left-16 w-5 h-5 bg-red-500 rounded-full border-2 border-white flex items-center justify-center">
                                <div class="w-1.5 h-1.5 bg-white rounded-full"></div>
                            </div>
                            <div class="absolute top-20 right-12 w-5 h-5 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center">
                                <div class="w-1.5 h-1.5 bg-white rounded-full"></div>
                            </div>
                            <div class="absolute bottom-16 left-12 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                <div class="w-1.5 h-1.5 bg-white rounded-full"></div>
                            </div>

                            <!-- 搜索框 -->
                            <div class="absolute top-3 left-3 right-3">
                                <div class="bg-white rounded-lg px-3 py-2 shadow-lg">
                                    <input type="text" placeholder="搜索地点" class="w-full text-sm focus:outline-none">
                                </div>
                            </div>

                            <!-- 定位按钮 -->
                            <button class="absolute bottom-3 right-3 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"/>
                                </svg>
                            </button>
                        </div>

                        <!-- 筛选条件 -->
                        <div class="bg-white px-4 py-3 border-b border-gray-200">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-bank-dark">筛选半径</span>
                                <span class="text-sm text-bank-blue">2公里</span>
                            </div>
                            <div class="flex space-x-2">
                                <button class="bg-bank-blue text-white px-3 py-1 rounded-full text-sm">2km</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm">5km</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm">10km</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm">自定义</button>
                            </div>
                        </div>

                        <!-- 企业列表 -->
                        <div class="flex-1 px-4 py-3">
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-sm font-medium text-bank-dark">附近企业 (23家)</span>
                                <button class="text-sm text-bank-blue">排序</button>
                            </div>

                            <div class="space-y-3">
                                <div class="bg-white rounded-lg p-3 shadow-sm">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-bank-dark text-sm">深圳市科技创新公司</h3>
                                            <p class="text-xs text-bank-gray mt-1">📍 距离 0.8km · 南山区科技园</p>
                                            <p class="text-xs text-bank-gray">📞 138****1234</p>
                                            <div class="flex items-center mt-2">
                                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">高新技术</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-1">A级信用</span>
                                            </div>
                                        </div>
                                        <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs ml-2">联系</button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-lg p-3 shadow-sm">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-bank-dark text-sm">智能制造有限公司</h3>
                                            <p class="text-xs text-bank-gray mt-1">📍 距离 1.2km · 南山区高新区</p>
                                            <p class="text-xs text-bank-gray">📞 139****5678</p>
                                            <div class="flex items-center mt-2">
                                                <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">专精特新</span>
                                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full ml-1">B级信用</span>
                                            </div>
                                        </div>
                                        <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs ml-2">联系</button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-lg p-3 shadow-sm">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-bank-dark text-sm">新能源科技股份公司</h3>
                                            <p class="text-xs text-bank-gray mt-1">📍 距离 1.8km · 南山区软件园</p>
                                            <p class="text-xs text-bank-gray">📞 137****9012</p>
                                            <div class="flex items-center mt-2">
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">科创贷</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-1">AAA信用</span>
                                            </div>
                                        </div>
                                        <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs ml-2">联系</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="mt-auto bg-white border-t border-gray-200">
                        <div class="grid grid-cols-4 py-2">
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">查企业</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-blue mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                </svg>
                                <span class="text-xs text-bank-blue font-medium">拓客户</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">做业务</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">我</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">拓客户页面 - 地图筛选</p>
        </div>

        <!-- 8. 拓客户页面 - 高级搜索 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <h1 class="text-lg font-semibold mb-3">拓客户</h1>
                        <div class="relative">
                            <input type="text" placeholder="搜索企业名称或关键词" class="w-full px-4 py-2 rounded-lg text-bank-dark placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white text-sm">
                            <svg class="absolute right-3 top-2 w-4 h-4 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Tab切换 -->
                    <div class="bg-white border-b border-gray-200">
                        <div class="flex">
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-transparent text-bank-gray text-sm">白名单</button>
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-transparent text-bank-gray text-sm">地图筛选</button>
                            <button class="flex-1 py-3 px-2 text-center border-b-2 border-bank-blue text-bank-blue font-medium text-sm">高级搜索</button>
                        </div>
                    </div>

                    <!-- 高级搜索内容 -->
                    <div class="flex-1 px-4 py-3 space-y-3">
                        <!-- 基本信息 -->
                        <div class="bg-white rounded-xl p-3 shadow-sm">
                            <h3 class="font-medium text-bank-dark mb-3 text-sm">基本信息</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs font-medium text-bank-dark mb-1">企业名称</label>
                                    <input type="text" placeholder="请输入企业名称关键词" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent text-sm">
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-xs font-medium text-bank-dark mb-1">所在地区</label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent text-sm">
                                            <option>请选择地区</option>
                                            <option>深圳市</option>
                                            <option>广州市</option>
                                            <option>东莞市</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-bank-dark mb-1">行业类型</label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent text-sm">
                                            <option>请选择行业</option>
                                            <option>信息技术</option>
                                            <option>制造业</option>
                                            <option>新能源</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 企业规模 -->
                        <div class="bg-white rounded-xl p-3 shadow-sm">
                            <h3 class="font-medium text-bank-dark mb-3 text-sm">企业规模</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs font-medium text-bank-dark mb-1">注册资本(万元)</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <input type="text" placeholder="最小值" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent text-sm">
                                        <input type="text" placeholder="最大值" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent text-sm">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-bank-dark mb-1">成立时间</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <input type="date" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent text-sm">
                                        <input type="date" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-bank-blue focus:border-transparent text-sm">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 企业标签 -->
                        <div class="bg-white rounded-xl p-3 shadow-sm">
                            <h3 class="font-medium text-bank-dark mb-3 text-sm">企业标签</h3>
                            <div class="grid grid-cols-2 gap-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-2">
                                    <span class="text-sm text-bank-dark">高新技术企业</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-2">
                                    <span class="text-sm text-bank-dark">专精特新</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-2">
                                    <span class="text-sm text-bank-dark">科创板上市</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-bank-blue focus:ring-bank-blue mr-2">
                                    <span class="text-sm text-bank-dark">独角兽企业</span>
                                </label>
                            </div>
                        </div>

                        <!-- 信用等级 -->
                        <div class="bg-white rounded-xl p-3 shadow-sm">
                            <h3 class="font-medium text-bank-dark mb-3 text-sm">信用等级</h3>
                            <div class="flex space-x-2">
                                <button class="bg-bank-blue text-white px-3 py-1 rounded-lg text-sm">全部</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-lg text-sm">AAA</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-lg text-sm">AA</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-lg text-sm">A</button>
                                <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-lg text-sm">B</button>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索按钮 -->
                    <div class="px-4 pb-3">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            开始搜索
                        </button>
                    </div>

                    <!-- 底部导航 -->
                    <div class="mt-auto bg-white border-t border-gray-200">
                        <div class="grid grid-cols-4 py-2">
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">查企业</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-blue mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                </svg>
                                <span class="text-xs text-bank-blue font-medium">拓客户</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">做业务</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">我</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">拓客户页面 - 高级搜索</p>
        </div>



        <!-- 9. 做业务主页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-4">
                        <h1 class="text-lg font-semibold mb-3">做业务</h1>
                        <div class="relative">
                            <input type="text" placeholder="搜索客户或任务" class="w-full px-4 py-2 rounded-lg text-bank-dark placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white text-sm">
                            <svg class="absolute right-3 top-2 w-4 h-4 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
                            </svg>
                        </div>
                    </div>

                    <!-- 功能菜单 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm overflow-hidden">
                        <div class="grid grid-cols-2 gap-px bg-gray-200">
                            <button class="bg-white p-4 hover:bg-gray-50">
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-2">
                                        <svg class="w-6 h-6 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium text-bank-dark">客户管理</span>
                                    <span class="text-xs text-bank-gray">拜访记录</span>
                                </div>
                            </button>
                            <button class="bg-white p-4 hover:bg-gray-50">
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-2">
                                        <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium text-bank-dark">授权管理</span>
                                    <span class="text-xs text-bank-gray">授权客户</span>
                                </div>
                            </button>
                            <button class="bg-white p-4 hover:bg-gray-50">
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-2">
                                        <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zM8 8a1 1 0 012 0v3a1 1 0 11-2 0V8z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium text-bank-dark">工作统计</span>
                                    <span class="text-xs text-bank-gray">数据分析</span>
                                </div>
                            </button>
                            <button class="bg-white p-4 hover:bg-gray-50">
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mb-2">
                                        <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium text-bank-dark">任务清单</span>
                                    <span class="text-xs text-bank-gray">日程安排</span>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- 企业福利 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold text-bank-dark">企业福利</h3>
                            <button class="text-sm text-bank-blue">查看全部</button>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                                <div class="w-10 h-10 bg-bank-blue rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark text-sm">科创贷优惠政策</p>
                                    <p class="text-xs text-bank-gray">最高1000万，利率优惠</p>
                                </div>
                                <button class="bg-bank-blue text-white px-3 py-1 rounded text-xs">发送</button>
                            </div>
                            <div class="flex items-center p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                                <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-bank-dark text-sm">专精特新扶持</p>
                                    <p class="text-xs text-bank-gray">专项资金支持</p>
                                </div>
                                <button class="bg-green-600 text-white px-3 py-1 rounded text-xs">发送</button>
                            </div>
                        </div>
                    </div>

                    <!-- 今日概览 -->
                    <div class="mx-4 mt-4 flex-1">
                        <h3 class="font-semibold text-bank-dark mb-3">今日概览</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="bg-white rounded-xl p-3 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xl font-bold text-bank-blue">8</p>
                                        <p class="text-xs text-bank-gray">待拜访客户</p>
                                    </div>
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-3 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xl font-bold text-green-600">5</p>
                                        <p class="text-xs text-bank-gray">已拜访客户</p>
                                    </div>
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-3 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xl font-bold text-purple-600">12</p>
                                        <p class="text-xs text-bank-gray">授权客户</p>
                                    </div>
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-3 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xl font-bold text-orange-600">3</p>
                                        <p class="text-xs text-bank-gray">待办任务</p>
                                    </div>
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="mt-auto bg-white border-t border-gray-200">
                        <div class="grid grid-cols-4 py-2">
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">查企业</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">拓客户</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-blue mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                </svg>
                                <span class="text-xs text-bank-blue font-medium">做业务</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">我</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">做业务主页</p>
        </div>

        <!-- 10. 客户管理页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                                </svg>
                                <h1 class="text-lg font-semibold">客户管理</h1>
                            </div>
                            <button class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">筛选</button>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <p class="text-xl font-bold text-bank-blue">156</p>
                                <p class="text-xs text-bank-gray">总客户数</p>
                            </div>
                            <div>
                                <p class="text-xl font-bold text-green-600">89</p>
                                <p class="text-xs text-bank-gray">本月拜访</p>
                            </div>
                            <div>
                                <p class="text-xl font-bold text-orange-600">23</p>
                                <p class="text-xs text-bank-gray">待拜访</p>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选标签 -->
                    <div class="px-4 py-3">
                        <div class="flex space-x-2 overflow-x-auto">
                            <button class="bg-bank-blue text-white px-3 py-1 rounded-full text-sm whitespace-nowrap">全部</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">今日拜访</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">本周拜访</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">待拜访</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">已成交</button>
                        </div>
                    </div>

                    <!-- 客户列表 -->
                    <div class="flex-1 px-4">
                        <div class="space-y-3">
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="font-semibold text-bank-dark">深圳市智能科技有限公司</h3>
                                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-2">已拜访</span>
                                        </div>
                                        <p class="text-sm text-bank-gray mb-1">联系人：张总 | 📞 138****8888</p>
                                        <p class="text-sm text-bank-gray mb-2">地址：深圳市南山区科技园</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-bank-gray">最近拜访：2024-01-15 14:30</span>
                                            <div class="flex space-x-2">
                                                <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">查看详情</button>
                                                <button class="bg-green-600 text-white px-2 py-1 rounded text-xs">再次拜访</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="font-semibold text-bank-dark">广州创新制造股份公司</h3>
                                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full ml-2">待拜访</span>
                                        </div>
                                        <p class="text-sm text-bank-gray mb-1">联系人：李总 | 📞 139****9999</p>
                                        <p class="text-sm text-bank-gray mb-2">地址：广州市天河区珠江新城</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-bank-gray">预约时间：2024-01-16 10:00</span>
                                            <div class="flex space-x-2">
                                                <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">查看详情</button>
                                                <button class="bg-orange-600 text-white px-2 py-1 rounded text-xs">开始拜访</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="font-semibold text-bank-dark">东莞市新能源科技公司</h3>
                                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full ml-2">已成交</span>
                                        </div>
                                        <p class="text-sm text-bank-gray mb-1">联系人：王总 | 📞 137****7777</p>
                                        <p class="text-sm text-bank-gray mb-2">地址：东莞市松山湖高新区</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-bank-gray">成交时间：2024-01-10 16:20</span>
                                            <div class="flex space-x-2">
                                                <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">查看详情</button>
                                                <button class="bg-purple-600 text-white px-2 py-1 rounded text-xs">跟进服务</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 添加客户按钮 -->
                    <div class="px-4 pb-4">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            + 添加新客户
                        </button>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">客户管理页</p>
        </div>

        <!-- 11. 授权管理页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                                </svg>
                                <h1 class="text-lg font-semibold">授权管理</h1>
                            </div>
                            <button class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">筛选</button>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <p class="text-xl font-bold text-green-600">45</p>
                                <p class="text-xs text-bank-gray">已授权</p>
                            </div>
                            <div>
                                <p class="text-xl font-bold text-orange-600">12</p>
                                <p class="text-xs text-bank-gray">待授权</p>
                            </div>
                            <div>
                                <p class="text-xl font-bold text-red-600">8</p>
                                <p class="text-xs text-bank-gray">即将过期</p>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选标签 -->
                    <div class="px-4 py-3">
                        <div class="flex space-x-2 overflow-x-auto">
                            <button class="bg-bank-blue text-white px-3 py-1 rounded-full text-sm whitespace-nowrap">全部</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">已授权</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">待授权</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">即将过期</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm whitespace-nowrap">已过期</button>
                        </div>
                    </div>

                    <!-- 授权列表 -->
                    <div class="flex-1 px-4">
                        <div class="space-y-3">
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="font-semibold text-bank-dark">深圳市智能科技有限公司</h3>
                                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-2">已授权</span>
                                        </div>
                                        <p class="text-sm text-bank-gray mb-1">授权类型：征信报告 + 财务报告</p>
                                        <p class="text-sm text-bank-gray mb-1">授权时间：2024-01-10 14:30</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-green-600">有效期至：2024-07-10</span>
                                            <div class="flex space-x-2">
                                                <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">查看报告</button>
                                                <button class="bg-green-600 text-white px-2 py-1 rounded text-xs">续期</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="font-semibold text-bank-dark">广州创新制造股份公司</h3>
                                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full ml-2">待授权</span>
                                        </div>
                                        <p class="text-sm text-bank-gray mb-1">申请类型：征信报告</p>
                                        <p class="text-sm text-bank-gray mb-1">申请时间：2024-01-15 10:20</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-orange-600">等待客户确认</span>
                                            <div class="flex space-x-2">
                                                <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">查看详情</button>
                                                <button class="bg-orange-600 text-white px-2 py-1 rounded text-xs">催促授权</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm border-l-4 border-red-500">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="font-semibold text-bank-dark">东莞市新能源科技公司</h3>
                                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full ml-2">即将过期</span>
                                        </div>
                                        <p class="text-sm text-bank-gray mb-1">授权类型：财务报告 + 经营报告</p>
                                        <p class="text-sm text-bank-gray mb-1">授权时间：2023-07-20 16:45</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-red-600">有效期至：2024-01-20 (5天后过期)</span>
                                            <div class="flex space-x-2">
                                                <button class="bg-bank-blue text-white px-2 py-1 rounded text-xs">查看报告</button>
                                                <button class="bg-red-600 text-white px-2 py-1 rounded text-xs">申请续期</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 发送授权申请按钮 -->
                    <div class="px-4 pb-4">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            + 发送授权申请
                        </button>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">授权管理页</p>
        </div>

        <!-- 12. 工作统计页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                                </svg>
                                <h1 class="text-lg font-semibold">工作统计</h1>
                            </div>
                            <button class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">导出</button>
                        </div>
                    </div>

                    <!-- 时间筛选 -->
                    <div class="bg-white px-4 py-3 border-b border-gray-200">
                        <div class="flex space-x-2">
                            <button class="bg-bank-blue text-white px-3 py-1 rounded-full text-sm">本周</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm">本月</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm">本季度</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-1 rounded-full text-sm">自定义</button>
                        </div>
                    </div>

                    <!-- 核心指标 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="font-semibold text-bank-dark mb-4">核心指标 (本周)</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-3 bg-blue-50 rounded-lg">
                                <p class="text-2xl font-bold text-bank-blue">23</p>
                                <p class="text-sm text-bank-gray">待拜访客户</p>
                                <p class="text-xs text-green-600">↑ 15% vs 上周</p>
                            </div>
                            <div class="text-center p-3 bg-green-50 rounded-lg">
                                <p class="text-2xl font-bold text-green-600">18</p>
                                <p class="text-sm text-bank-gray">已拜访客户</p>
                                <p class="text-xs text-green-600">↑ 20% vs 上周</p>
                            </div>
                            <div class="text-center p-3 bg-purple-50 rounded-lg">
                                <p class="text-2xl font-bold text-purple-600">12</p>
                                <p class="text-sm text-bank-gray">授权客户</p>
                                <p class="text-xs text-red-600">↓ 8% vs 上周</p>
                            </div>
                            <div class="text-center p-3 bg-orange-50 rounded-lg">
                                <p class="text-2xl font-bold text-orange-600">5</p>
                                <p class="text-sm text-bank-gray">成交客户</p>
                                <p class="text-xs text-green-600">↑ 25% vs 上周</p>
                            </div>
                        </div>
                    </div>

                    <!-- 趋势图表 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="font-semibold text-bank-dark mb-4">拜访趋势</h3>
                        <div class="h-32 bg-gradient-to-t from-blue-50 to-transparent rounded-lg relative">
                            <img src="https://images.unsplash.com/photo-**********-bebda4e38f71?w=300&h=120&fit=crop" alt="图表" class="w-full h-full object-cover rounded-lg opacity-60">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <p class="text-bank-gray text-sm">拜访量趋势图</p>
                            </div>
                        </div>
                    </div>

                    <!-- 详细数据 -->
                    <div class="flex-1 px-4 mt-4">
                        <h3 class="font-semibold text-bank-dark mb-3">详细数据</h3>
                        <div class="space-y-3">
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-bank-dark">周一 (1月15日)</h4>
                                    <span class="text-sm text-bank-gray">5个客户</span>
                                </div>
                                <div class="grid grid-cols-3 gap-3 text-center">
                                    <div>
                                        <p class="text-lg font-bold text-green-600">3</p>
                                        <p class="text-xs text-bank-gray">已拜访</p>
                                    </div>
                                    <div>
                                        <p class="text-lg font-bold text-orange-600">2</p>
                                        <p class="text-xs text-bank-gray">待拜访</p>
                                    </div>
                                    <div>
                                        <p class="text-lg font-bold text-purple-600">1</p>
                                        <p class="text-xs text-bank-gray">新授权</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-bank-dark">周二 (1月16日)</h4>
                                    <span class="text-sm text-bank-gray">4个客户</span>
                                </div>
                                <div class="grid grid-cols-3 gap-3 text-center">
                                    <div>
                                        <p class="text-lg font-bold text-green-600">4</p>
                                        <p class="text-xs text-bank-gray">已拜访</p>
                                    </div>
                                    <div>
                                        <p class="text-lg font-bold text-orange-600">0</p>
                                        <p class="text-xs text-bank-gray">待拜访</p>
                                    </div>
                                    <div>
                                        <p class="text-lg font-bold text-purple-600">2</p>
                                        <p class="text-xs text-bank-gray">新授权</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-bank-dark">周三 (1月17日)</h4>
                                    <span class="text-sm text-bank-gray">6个客户</span>
                                </div>
                                <div class="grid grid-cols-3 gap-3 text-center">
                                    <div>
                                        <p class="text-lg font-bold text-green-600">4</p>
                                        <p class="text-xs text-bank-gray">已拜访</p>
                                    </div>
                                    <div>
                                        <p class="text-lg font-bold text-orange-600">2</p>
                                        <p class="text-xs text-bank-gray">待拜访</p>
                                    </div>
                                    <div>
                                        <p class="text-lg font-bold text-purple-600">1</p>
                                        <p class="text-xs text-bank-gray">新授权</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">工作统计页</p>
        </div>

        <!-- 13. 任务清单页 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 顶部导航 -->
                    <div class="bg-bank-blue text-white px-4 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                                </svg>
                                <h1 class="text-lg font-semibold">任务清单</h1>
                            </div>
                            <button class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">日历</button>
                        </div>
                    </div>

                    <!-- 日期选择 -->
                    <div class="bg-white px-4 py-3 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold text-bank-dark">2024年1月</h3>
                            <div class="flex space-x-2">
                                <button class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100">
                                    <svg class="w-4 h-4 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                                    </svg>
                                </button>
                                <button class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100">
                                    <svg class="w-4 h-4 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex space-x-2 overflow-x-auto">
                            <button class="bg-gray-100 text-bank-gray px-3 py-2 rounded-lg text-sm whitespace-nowrap">15日 周一</button>
                            <button class="bg-bank-blue text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap">16日 周二</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-2 rounded-lg text-sm whitespace-nowrap">17日 周三</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-2 rounded-lg text-sm whitespace-nowrap">18日 周四</button>
                            <button class="bg-gray-100 text-bank-gray px-3 py-2 rounded-lg text-sm whitespace-nowrap">19日 周五</button>
                        </div>
                    </div>

                    <!-- 今日概览 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
                        <h3 class="font-semibold text-bank-dark mb-3">今日概览 (1月16日)</h3>
                        <div class="grid grid-cols-3 gap-3 text-center">
                            <div>
                                <p class="text-xl font-bold text-bank-blue">8</p>
                                <p class="text-xs text-bank-gray">总任务</p>
                            </div>
                            <div>
                                <p class="text-xl font-bold text-green-600">5</p>
                                <p class="text-xs text-bank-gray">已完成</p>
                            </div>
                            <div>
                                <p class="text-xl font-bold text-orange-600">3</p>
                                <p class="text-xs text-bank-gray">待完成</p>
                            </div>
                        </div>
                    </div>

                    <!-- 任务列表 -->
                    <div class="flex-1 px-4 mt-4">
                        <div class="space-y-3">
                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start">
                                    <input type="checkbox" checked class="mt-1 mr-3 rounded border-gray-300 text-green-600 focus:ring-green-500">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-bank-dark line-through text-gray-500">拜访深圳市智能科技有限公司</h4>
                                        <p class="text-sm text-bank-gray mt-1">时间：09:00-10:00</p>
                                        <p class="text-sm text-bank-gray">地址：南山区科技园</p>
                                        <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mt-2">已完成</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm border-l-4 border-orange-500">
                                <div class="flex items-start">
                                    <input type="checkbox" class="mt-1 mr-3 rounded border-gray-300 text-bank-blue focus:ring-bank-blue">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-bank-dark">拜访广州创新制造股份公司</h4>
                                        <p class="text-sm text-bank-gray mt-1">时间：14:00-15:30</p>
                                        <p class="text-sm text-bank-gray">地址：天河区珠江新城</p>
                                        <span class="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full mt-2">进行中</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start">
                                    <input type="checkbox" class="mt-1 mr-3 rounded border-gray-300 text-bank-blue focus:ring-bank-blue">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-bank-dark">整理客户拜访报告</h4>
                                        <p class="text-sm text-bank-gray mt-1">时间：16:00-17:00</p>
                                        <p class="text-sm text-bank-gray">备注：总结今日拜访情况</p>
                                        <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-2">待开始</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm">
                                <div class="flex items-start">
                                    <input type="checkbox" class="mt-1 mr-3 rounded border-gray-300 text-bank-blue focus:ring-bank-blue">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-bank-dark">发送授权申请邮件</h4>
                                        <p class="text-sm text-bank-gray mt-1">时间：17:30-18:00</p>
                                        <p class="text-sm text-bank-gray">客户：东莞市新能源科技公司</p>
                                        <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-2">待开始</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 添加任务按钮 -->
                    <div class="px-4 pb-4">
                        <button class="w-full bg-bank-blue text-white py-3 rounded-lg font-medium">
                            + 添加新任务
                        </button>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">任务清单页</p>
        </div>

        <!-- 14. 我的页面 -->
        <div class="page-container">
            <div class="mockup-frame phone-frame">
                <div class="h-full bg-bank-light-gray flex flex-col">
                    <!-- 个人信息卡片 -->
                    <div class="bg-gradient-to-br from-bank-blue to-bank-light-blue text-white px-4 py-6">
                        <div class="flex items-center">
                            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mr-4">
                                <span class="text-bank-blue text-xl font-bold">张</span>
                            </div>
                            <div class="flex-1">
                                <h2 class="text-lg font-semibold">张经理</h2>
                                <p class="text-sm opacity-90">深圳分行 · 客户经理</p>
                                <p class="text-sm opacity-80">工号：BM20240001</p>
                            </div>
                            <button class="bg-white bg-opacity-20 p-2 rounded-full">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                                </svg>
                            </button>
                        </div>

                        <!-- 工作数据 -->
                        <div class="grid grid-cols-3 gap-4 mt-6">
                            <div class="text-center">
                                <p class="text-2xl font-bold">156</p>
                                <p class="text-xs opacity-80">管理客户</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold">89</p>
                                <p class="text-xs opacity-80">本月拜访</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold">23</p>
                                <p class="text-xs opacity-80">成交客户</p>
                            </div>
                        </div>
                    </div>

                    <!-- 功能菜单 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm overflow-hidden">
                        <div class="divide-y divide-gray-100">
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-bank-blue" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">个人资料</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zM8 8a1 1 0 012 0v3a1 1 0 11-2 0V8z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">工作报告</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">系统设置</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 帮助与支持 -->
                    <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm overflow-hidden">
                        <div class="divide-y divide-gray-100">
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">帮助中心</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">意见反馈</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                            <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z"/>
                                        </svg>
                                    </div>
                                    <span class="text-bank-dark font-medium">退出登录</span>
                                </div>
                                <svg class="w-5 h-5 text-bank-gray" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 版本信息 -->
                    <div class="mx-4 mt-4 text-center">
                        <p class="text-sm text-bank-gray">深企信银行版 v1.0.0</p>
                        <p class="text-xs text-bank-gray mt-1">© 2024 深圳银行. 保留所有权利</p>
                    </div>

                    <!-- 底部导航 -->
                    <div class="mt-auto bg-white border-t border-gray-200">
                        <div class="grid grid-cols-4 py-2">
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">查企业</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">拓客户</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-gray mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                                </svg>
                                <span class="text-xs text-bank-gray">做业务</span>
                            </div>
                            <div class="text-center py-2">
                                <svg class="w-6 h-6 text-bank-blue mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                <span class="text-xs text-bank-blue font-medium">我</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center mt-2 text-sm text-bank-gray">我的页面</p>
        </div>

    </div>
</body>
</html>
